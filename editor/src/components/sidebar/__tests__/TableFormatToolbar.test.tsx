import { describe, expect, it } from "vitest";
import type { TableCell } from "../../../types/table";

// Helper function to create a test table
function createTestTable(): TableCell[][] {
	return [
		[
			{
				content: "A1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "B1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "C1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "D1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
		],
		[
			{
				content: "A2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "B2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "C2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
			{
				content: "D2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
			},
		],
	];
}

describe("TableFormatToolbar - Non-destructive Colspan", () => {
	it("should preserve cell data when increasing and then decreasing colspan", () => {
		const cells = createTestTable();

		// Original state: A1, B1, C1, D1
		expect(cells[0]).toHaveLength(4);
		expect(cells[0][0].content).toBe("A1");
		expect(cells[0][1].content).toBe("B1");
		expect(cells[0][2].content).toBe("C1");
		expect(cells[0][3].content).toBe("D1");

		// Simulate increasing colspan of A1 from 1 to 3 (should hide B1 and C1)
		const targetCell = cells[0][0];
		const hiddenCells: TableCell[] = [];

		// Store B1 and C1 as hidden
		hiddenCells.push({ ...cells[0][1] }); // B1
		hiddenCells.push({ ...cells[0][2] }); // C1

		// Remove B1 and C1 from the array
		cells[0].splice(1, 2);

		// Update the target cell
		targetCell.colspan = 3;
		targetCell.hiddenCells = hiddenCells;

		// After increasing colspan: A1(colspan=3), D1
		expect(cells[0]).toHaveLength(2);
		expect(cells[0][0].content).toBe("A1");
		expect(cells[0][0].colspan).toBe(3);
		expect(cells[0][0].hiddenCells).toHaveLength(2);
		expect(cells[0][0].hiddenCells?.[0].content).toBe("B1");
		expect(cells[0][0].hiddenCells?.[1].content).toBe("C1");
		expect(cells[0][1].content).toBe("D1");

		// Simulate decreasing colspan back to 1 (should restore B1 and C1)
		const storedCells = targetCell.hiddenCells || [];

		// Restore the hidden cells
		for (let i = 0; i < storedCells.length; i++) {
			cells[0].splice(1 + i, 0, { ...storedCells[i] });
		}

		// Update the target cell
		targetCell.colspan = 1;
		targetCell.hiddenCells = undefined;

		// After decreasing colspan: should be back to A1, B1, C1, D1
		expect(cells[0]).toHaveLength(4);
		expect(cells[0][0].content).toBe("A1");
		expect(cells[0][0].colspan).toBe(1);
		expect(cells[0][0].hiddenCells).toBeUndefined();
		expect(cells[0][1].content).toBe("B1");
		expect(cells[0][2].content).toBe("C1");
		expect(cells[0][3].content).toBe("D1");
	});

	it("should handle partial restoration when decreasing colspan", () => {
		const cells = createTestTable();
		const targetCell = cells[0][0];

		// Simulate increasing colspan from 1 to 4 (hide B1, C1, D1)
		const hiddenCells: TableCell[] = [
			{ ...cells[0][1] }, // B1
			{ ...cells[0][2] }, // C1
			{ ...cells[0][3] }, // D1
		];

		cells[0].splice(1, 3); // Remove B1, C1, D1
		targetCell.colspan = 4;
		targetCell.hiddenCells = hiddenCells;

		// Now decrease colspan from 4 to 2 (should restore only B1)
		const restoredCell = { ...hiddenCells[0] };
		cells[0].splice(1, 0, restoredCell);

		// Update hidden cells and colspan
		targetCell.hiddenCells = hiddenCells.slice(1); // Keep C1, D1 hidden
		targetCell.colspan = 2;

		// Should have A1(colspan=2), B1 restored, but C1 and D1 still hidden
		expect(cells[0]).toHaveLength(2);
		expect(cells[0][0].content).toBe("A1");
		expect(cells[0][0].colspan).toBe(2);
		expect(cells[0][0].hiddenCells).toHaveLength(2);
		expect(cells[0][0].hiddenCells?.[0].content).toBe("C1");
		expect(cells[0][0].hiddenCells?.[1].content).toBe("D1");
		expect(cells[0][1].content).toBe("B1");
	});
});
