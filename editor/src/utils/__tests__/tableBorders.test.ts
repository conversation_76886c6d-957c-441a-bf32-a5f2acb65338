import { describe, expect, it } from "vitest";
import type { TableCell, TableCellBorder } from "@/types/table";
import {
	applyBordersToSelection,
	applyCellBorderSide,
	copyBorderSetting,
	restoreBordersForDeletedColumn,
	restoreBordersForDeletedRow,
	shiftBordersForNewColumn,
	shiftBordersForNewRow,
} from "../tableUtils";

// Helper function to create test cells
const createTestCell = (
	content = "Test",
	borderSettings?: Partial<{
		top: TableCellBorder;
		right: TableCellBorder;
		bottom: TableCellBorder;
		left: TableCellBorder;
	}>,
	borderWidths = { top: 1, right: 1, bottom: 1, left: 1 },
): TableCell => ({
	content,
	colspan: 1,
	rowspan: 1,
	backgroundColor: null,
	borderWidths,
	borderSettings,
	verticalAlign: "top",
});

describe("Table Border Utilities", () => {
	describe("applyCellBorderSide", () => {
		it("should apply border when line is active", () => {
			const cell = createTestCell();
			applyCellBorderSide(cell, "top", true, 2, "#ff0000");

			expect(cell.borderSettings?.top).toEqual({
				width: 2,
				color: "#ff0000",
			});
		});

		it("should not modify border when line is inactive", () => {
			const cell = createTestCell();
			cell.borderSettings = { top: { width: 3, color: "#00ff00" } };

			applyCellBorderSide(cell, "top", false, 2, "#ff0000");

			expect(cell.borderSettings?.top).toEqual({
				width: 3,
				color: "#00ff00",
			});
		});

		it("should remove border when width is 0", () => {
			const cell = createTestCell();
			cell.borderSettings = { top: { width: 3, color: "#00ff00" } };

			applyCellBorderSide(cell, "top", true, 0, null);

			expect(cell.borderSettings?.top).toBeUndefined();
			expect(cell.borderWidths.top).toBe(0);
		});

		it("should preserve existing color when only width is changed", () => {
			const cell = createTestCell();
			cell.borderSettings = { top: { width: 1, color: "#00ff00" } };

			applyCellBorderSide(cell, "top", true, 3, null);

			expect(cell.borderSettings?.top).toEqual({
				width: 3,
				color: "#00ff00",
			});
		});
	});

	describe("applyBordersToSelection", () => {
		it("should apply outer borders to single cell selection", () => {
			const cells = [[createTestCell()]];
			const selection = { start: { row: 0, col: 0 }, end: { row: 0, col: 0 } };
			const activeLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: false,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				2,
				"#ff0000",
				1,
				1,
			);

			const cell = result[0][0];
			expect(cell.borderSettings?.top?.width).toBe(2);
			expect(cell.borderSettings?.bottom?.width).toBe(2);
			expect(cell.borderSettings?.left?.width).toBe(2);
			expect(cell.borderSettings?.right?.width).toBe(2);
		});

		it("should preserve borders from previous selections when applying new borders", () => {
			// Create a 2x2 table
			const cells = [
				[createTestCell(), createTestCell()],
				[createTestCell(), createTestCell()],
			];

			// First selection: apply borders to top-left cell
			const firstSelection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 0 },
			};
			const firstActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: false,
			};

			const cellsAfterFirst = applyBordersToSelection(
				cells,
				firstSelection,
				firstActiveLines,
				3,
				"#ff0000",
				2,
				2,
			);

			// Verify first selection applied correctly
			expect(cellsAfterFirst[0][0].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterFirst[0][0].borderSettings?.bottom?.width).toBe(3);
			expect(cellsAfterFirst[0][0].borderSettings?.left?.width).toBe(3);
			expect(cellsAfterFirst[0][0].borderSettings?.right?.width).toBe(3);

			// Second selection: apply borders to bottom-right cell
			const secondSelection = {
				start: { row: 1, col: 1 },
				end: { row: 1, col: 1 },
			};
			const secondActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: false,
			};

			const cellsAfterSecond = applyBordersToSelection(
				cellsAfterFirst, // Use the result from the first operation
				secondSelection,
				secondActiveLines,
				2,
				"#00ff00",
				2,
				2,
			);

			// Verify first selection borders are still preserved
			expect(cellsAfterSecond[0][0].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterSecond[0][0].borderSettings?.bottom?.width).toBe(3);
			expect(cellsAfterSecond[0][0].borderSettings?.left?.width).toBe(3);
			expect(cellsAfterSecond[0][0].borderSettings?.right?.width).toBe(3);
			expect(cellsAfterSecond[0][0].borderSettings?.top?.color).toBe("#ff0000");

			// Verify second selection borders are applied
			expect(cellsAfterSecond[1][1].borderSettings?.top?.width).toBe(2);
			expect(cellsAfterSecond[1][1].borderSettings?.bottom?.width).toBe(2);
			expect(cellsAfterSecond[1][1].borderSettings?.left?.width).toBe(2);
			expect(cellsAfterSecond[1][1].borderSettings?.right?.width).toBe(2);
			expect(cellsAfterSecond[1][1].borderSettings?.top?.color).toBe("#00ff00");
		});

		it("should simulate the bug scenario: multiple cell selections with border changes", () => {
			// Create a 3x3 table to simulate the bug scenario
			const cells = [
				[createTestCell(), createTestCell(), createTestCell()],
				[createTestCell(), createTestCell(), createTestCell()],
				[createTestCell(), createTestCell(), createTestCell()],
			];

			// Step 1: Select multiple cells (top row) and set their borders
			const firstSelection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 2 },
			};
			const firstActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: true,
			};

			const cellsAfterFirstSelection = applyBordersToSelection(
				cells,
				firstSelection,
				firstActiveLines,
				3,
				"#ff0000",
				3,
				3,
			);

			// Verify first selection borders are applied
			expect(cellsAfterFirstSelection[0][0].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterFirstSelection[0][1].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterFirstSelection[0][2].borderSettings?.top?.width).toBe(3);

			// Step 2: Select other cells (bottom row) and set their borders
			const secondSelection = {
				start: { row: 2, col: 0 },
				end: { row: 2, col: 2 },
			};
			const secondActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: true,
			};

			const cellsAfterSecondSelection = applyBordersToSelection(
				cellsAfterFirstSelection, // Use the result from the first operation
				secondSelection,
				secondActiveLines,
				2,
				"#00ff00",
				3,
				3,
			);

			// Step 3: Verify that borders from step 1 are still preserved
			expect(cellsAfterSecondSelection[0][0].borderSettings?.top?.width).toBe(
				3,
			);
			expect(cellsAfterSecondSelection[0][0].borderSettings?.top?.color).toBe(
				"#ff0000",
			);
			expect(cellsAfterSecondSelection[0][1].borderSettings?.top?.width).toBe(
				3,
			);
			expect(cellsAfterSecondSelection[0][1].borderSettings?.top?.color).toBe(
				"#ff0000",
			);
			expect(cellsAfterSecondSelection[0][2].borderSettings?.top?.width).toBe(
				3,
			);
			expect(cellsAfterSecondSelection[0][2].borderSettings?.top?.color).toBe(
				"#ff0000",
			);

			// Step 4: Verify that borders from step 2 are applied
			expect(
				cellsAfterSecondSelection[2][0].borderSettings?.bottom?.width,
			).toBe(2);
			expect(
				cellsAfterSecondSelection[2][0].borderSettings?.bottom?.color,
			).toBe("#00ff00");
			expect(
				cellsAfterSecondSelection[2][1].borderSettings?.bottom?.width,
			).toBe(2);
			expect(
				cellsAfterSecondSelection[2][1].borderSettings?.bottom?.color,
			).toBe("#00ff00");
			expect(
				cellsAfterSecondSelection[2][2].borderSettings?.bottom?.width,
			).toBe(2);
			expect(
				cellsAfterSecondSelection[2][2].borderSettings?.bottom?.color,
			).toBe("#00ff00");
		});

		it("should simulate the exact bug scenario: borders disappearing when not using updated state", () => {
			// This test simulates what would happen with the old buggy behavior
			// where each border operation starts from the original cells instead of the updated state
			const originalCells = [
				[createTestCell(), createTestCell()],
				[createTestCell(), createTestCell()],
			];

			// First selection: apply borders to top row
			const firstSelection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 1 },
			};
			const firstActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: true,
			};

			const cellsAfterFirst = applyBordersToSelection(
				originalCells,
				firstSelection,
				firstActiveLines,
				3,
				"#ff0000",
				2,
				2,
			);

			// Verify first borders are applied
			expect(cellsAfterFirst[0][0].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterFirst[0][1].borderSettings?.top?.width).toBe(3);

			// Second selection: apply borders to bottom row
			// CORRECT behavior: use the result from the first operation
			const secondSelection = {
				start: { row: 1, col: 0 },
				end: { row: 1, col: 1 },
			};
			const secondActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: true,
			};

			const cellsAfterSecondCorrect = applyBordersToSelection(
				cellsAfterFirst, // Use updated state - CORRECT
				secondSelection,
				secondActiveLines,
				2,
				"#00ff00",
				2,
				2,
			);

			// BUGGY behavior: start from original cells again (simulating the old bug)
			const cellsAfterSecondBuggy = applyBordersToSelection(
				originalCells, // Use original state - BUGGY
				secondSelection,
				secondActiveLines,
				2,
				"#00ff00",
				2,
				2,
			);

			// With correct behavior: first borders should be preserved
			expect(cellsAfterSecondCorrect[0][0].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterSecondCorrect[0][0].borderSettings?.top?.color).toBe(
				"#ff0000",
			);
			expect(cellsAfterSecondCorrect[0][1].borderSettings?.top?.width).toBe(3);
			expect(cellsAfterSecondCorrect[0][1].borderSettings?.top?.color).toBe(
				"#ff0000",
			);

			// With buggy behavior: first borders would be missing
			expect(cellsAfterSecondBuggy[0][0].borderSettings?.top).toBeUndefined();
			expect(cellsAfterSecondBuggy[0][1].borderSettings?.top).toBeUndefined();

			// Both should have the second borders applied
			expect(cellsAfterSecondCorrect[1][0].borderSettings?.bottom?.width).toBe(
				2,
			);
			expect(cellsAfterSecondBuggy[1][0].borderSettings?.bottom?.width).toBe(2);
		});

		it("should simulate the complete UI workflow: multiple border operations with state preservation", () => {
			// This test simulates the complete workflow described in the bug report:
			// 1. Select multiple cells
			// 2. Set their borders
			// 3. Select other cells
			// 4. Set their borders
			// 5. Verify borders from step 2 are preserved

			const originalCells = [
				[createTestCell(), createTestCell(), createTestCell()],
				[createTestCell(), createTestCell(), createTestCell()],
				[createTestCell(), createTestCell(), createTestCell()],
			];

			// Step 1 & 2: Select multiple cells (first row) and set their borders
			const firstSelection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 2 },
			};
			const firstActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: true, // Inner vertical lines for multi-cell selection
			};

			// Apply first border operation
			let currentState = applyBordersToSelection(
				originalCells,
				firstSelection,
				firstActiveLines,
				3, // 3pt width
				"#ff0000", // Red color
				3,
				3,
			);

			// Verify first borders are applied correctly
			expect(currentState[0][0].borderSettings?.top?.width).toBe(3);
			expect(currentState[0][0].borderSettings?.top?.color).toBe("#ff0000");
			expect(currentState[0][1].borderSettings?.top?.width).toBe(3);
			expect(currentState[0][1].borderSettings?.top?.color).toBe("#ff0000");
			expect(currentState[0][2].borderSettings?.top?.width).toBe(3);
			expect(currentState[0][2].borderSettings?.top?.color).toBe("#ff0000");

			// Verify inner vertical borders between cells in first row
			expect(currentState[0][0].borderSettings?.right?.width).toBe(3);
			expect(currentState[0][1].borderSettings?.right?.width).toBe(3);

			// Step 3 & 4: Select other cells (last row) and set their borders
			const secondSelection = {
				start: { row: 2, col: 0 },
				end: { row: 2, col: 2 },
			};
			const secondActiveLines = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: true,
			};

			// Apply second border operation using the updated state from first operation
			currentState = applyBordersToSelection(
				currentState, // Use the updated state, not original cells
				secondSelection,
				secondActiveLines,
				2, // 2pt width
				"#00ff00", // Green color
				3,
				3,
			);

			// Step 5: Verify borders from step 2 are still preserved
			expect(currentState[0][0].borderSettings?.top?.width).toBe(3);
			expect(currentState[0][0].borderSettings?.top?.color).toBe("#ff0000");
			expect(currentState[0][1].borderSettings?.top?.width).toBe(3);
			expect(currentState[0][1].borderSettings?.top?.color).toBe("#ff0000");
			expect(currentState[0][2].borderSettings?.top?.width).toBe(3);
			expect(currentState[0][2].borderSettings?.top?.color).toBe("#ff0000");

			// Verify inner vertical borders from first operation are preserved
			expect(currentState[0][0].borderSettings?.right?.width).toBe(3);
			expect(currentState[0][0].borderSettings?.right?.color).toBe("#ff0000");
			expect(currentState[0][1].borderSettings?.right?.width).toBe(3);
			expect(currentState[0][1].borderSettings?.right?.color).toBe("#ff0000");

			// Verify second borders are applied correctly
			expect(currentState[2][0].borderSettings?.bottom?.width).toBe(2);
			expect(currentState[2][0].borderSettings?.bottom?.color).toBe("#00ff00");
			expect(currentState[2][1].borderSettings?.bottom?.width).toBe(2);
			expect(currentState[2][1].borderSettings?.bottom?.color).toBe("#00ff00");
			expect(currentState[2][2].borderSettings?.bottom?.width).toBe(2);
			expect(currentState[2][2].borderSettings?.bottom?.color).toBe("#00ff00");

			// Verify inner vertical borders from second operation
			expect(currentState[2][0].borderSettings?.right?.width).toBe(2);
			expect(currentState[2][0].borderSettings?.right?.color).toBe("#00ff00");
			expect(currentState[2][1].borderSettings?.right?.width).toBe(2);
			expect(currentState[2][1].borderSettings?.right?.color).toBe("#00ff00");

			// Additional verification: middle row should not have any borders
			expect(currentState[1][0].borderSettings).toBeUndefined();
			expect(currentState[1][1].borderSettings).toBeUndefined();
			expect(currentState[1][2].borderSettings).toBeUndefined();
		});

		it("should verify the fix for the reported bug: selection change preserves previous borders", () => {
			// This test specifically verifies that changing cell selection doesn't reset border state
			// It simulates the exact issue: borders disappearing when selection changes

			const originalCells = [
				[createTestCell(), createTestCell()],
				[createTestCell(), createTestCell()],
			];

			// Step 1: Apply borders to first selection (top row)
			const firstSelection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 1 },
			};
			const borderSettings = {
				outerTop: true,
				outerBottom: true,
				outerLeft: true,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: true,
			};

			const stateAfterFirstBorders = applyBordersToSelection(
				originalCells,
				firstSelection,
				borderSettings,
				3,
				"#ff0000",
				2,
				2,
			);

			// Verify first borders are applied
			expect(stateAfterFirstBorders[0][0].borderSettings?.top?.width).toBe(3);
			expect(stateAfterFirstBorders[0][0].borderSettings?.top?.color).toBe(
				"#ff0000",
			);
			expect(stateAfterFirstBorders[0][1].borderSettings?.top?.width).toBe(3);
			expect(stateAfterFirstBorders[0][1].borderSettings?.top?.color).toBe(
				"#ff0000",
			);

			// Step 2: Simulate selection change (this is where the bug occurred)
			// In the real UI, this would trigger a re-render with different selection
			// but the same table ID. Our fix should preserve the border state.

			// Step 3: Apply borders to second selection (bottom row) using the preserved state
			const secondSelection = {
				start: { row: 1, col: 0 },
				end: { row: 1, col: 1 },
			};

			const stateAfterSecondBorders = applyBordersToSelection(
				stateAfterFirstBorders, // Use the state with first borders - this is the key fix
				secondSelection,
				borderSettings,
				2,
				"#00ff00",
				2,
				2,
			);

			// Step 4: Verify that borders from step 1 are still preserved
			expect(stateAfterSecondBorders[0][0].borderSettings?.top?.width).toBe(3);
			expect(stateAfterSecondBorders[0][0].borderSettings?.top?.color).toBe(
				"#ff0000",
			);
			expect(stateAfterSecondBorders[0][1].borderSettings?.top?.width).toBe(3);
			expect(stateAfterSecondBorders[0][1].borderSettings?.top?.color).toBe(
				"#ff0000",
			);

			// Step 5: Verify that borders from step 3 are also applied
			expect(stateAfterSecondBorders[1][0].borderSettings?.bottom?.width).toBe(
				2,
			);
			expect(stateAfterSecondBorders[1][0].borderSettings?.bottom?.color).toBe(
				"#00ff00",
			);
			expect(stateAfterSecondBorders[1][1].borderSettings?.bottom?.width).toBe(
				2,
			);
			expect(stateAfterSecondBorders[1][1].borderSettings?.bottom?.color).toBe(
				"#00ff00",
			);

			// This test passing confirms that the bug is fixed:
			// - Borders from the first selection are preserved
			// - Borders from the second selection are applied
			// - No borders disappear when selection changes
		});

		it("should handle rowspan cells correctly", () => {
			// Create a 2x2 table where first cell spans 2 rows
			const cells = [
				[
					{ ...createTestCell("Cell with rowspan"), colspan: 1, rowspan: 2 },
					createTestCell(),
				],
				[createTestCell()], // Second row only has one cell due to rowspan
			];
			const selection = { start: { row: 0, col: 0 }, end: { row: 1, col: 1 } };
			const activeLines = {
				outerTop: true,
				outerBottom: false,
				outerLeft: false,
				outerRight: false,
				innerHorizontal: false,
				innerVertical: true,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				3,
				"#00ff00",
				2,
				2,
			);

			// The rowspan cell should have top border
			expect(result[0][0].borderSettings?.top?.width).toBe(3);
			// The rowspan cell should have right border (inner vertical)
			expect(result[0][0].borderSettings?.right?.width).toBe(3);
		});

		it("should handle colspan cells correctly", () => {
			// Create a 2x2 table where first cell spans 2 columns
			const cells = [
				[{ ...createTestCell("Cell with colspan"), colspan: 2, rowspan: 1 }], // First row only has one cell due to colspan
				[createTestCell(), createTestCell()],
			];
			const selection = { start: { row: 0, col: 0 }, end: { row: 1, col: 1 } };
			const activeLines = {
				outerTop: true,
				outerBottom: false,
				outerLeft: false,
				outerRight: false,
				innerHorizontal: true,
				innerVertical: false,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				4,
				"#0000ff",
				2,
				2,
			);

			// The colspan cell should have top border
			expect(result[0][0].borderSettings?.top?.width).toBe(4);
			// The colspan cell should have bottom border (inner horizontal)
			expect(result[0][0].borderSettings?.bottom?.width).toBe(4);
		});

		it("should preserve existing borders when not overridden", () => {
			const cell1 = createTestCell();
			cell1.borderSettings = { left: { width: 5, color: "#purple" } };

			const cells = [[cell1, createTestCell()]];
			const selection = { start: { row: 0, col: 0 }, end: { row: 0, col: 1 } };
			const activeLines = {
				outerTop: true,
				outerBottom: false,
				outerLeft: false, // Not active, should preserve existing
				outerRight: false,
				innerHorizontal: false,
				innerVertical: false,
			};

			const result = applyBordersToSelection(
				cells,
				selection,
				activeLines,
				2,
				"#ff0000",
				1,
				2,
			);

			// Should preserve the existing left border
			expect(result[0][0].borderSettings?.left).toEqual({
				width: 5,
				color: "#purple",
			});
			// Should apply the new top border
			expect(result[0][0].borderSettings?.top?.width).toBe(2);
		});
	});
});

describe("Border Shifting Functions", () => {
	describe("shiftBordersForNewRow", () => {
		it("should preserve border structure when adding a new row", () => {
			const originalCells = [
				[
					createTestCell("A1", { bottom: { width: 3, color: "#ff0000" } }),
					createTestCell("B1", { bottom: { width: 2, color: "#00ff00" } }),
				],
				[
					createTestCell("A2", { top: { width: 1, color: "#0000ff" } }),
					createTestCell("B2"),
				],
			];

			const result = shiftBordersForNewRow(originalCells, 1, "#000000");

			// The function should preserve existing border relationships
			// when inserting at index 1, it should handle the border relationship between row 0 and row 1
			expect(result).toHaveLength(2);

			// Check that borders are transferred to maintain visual continuity
			// The cell above (A1) should have its bottom border preserved or enhanced
			// to maintain the relationship that existed with the cell below (A2)
			const cellA1 = result[0][0];
			const cellA2 = result[1][0];

			// A1 should have maintained its bottom border (3px red)
			expect(cellA1.borderSettings?.bottom).toEqual({
				width: 3,
				color: "#ff0000",
			});

			// Since A2 had a top border, A1's bottom border should be enhanced to match
			// or A1 should keep its stronger border (3px vs 1px)
			if (cellA2.borderSettings?.top) {
				// A2's top border might be preserved or modified based on conflict resolution
				expect(cellA2.borderSettings.top.width).toBeGreaterThanOrEqual(1);
			}
		});

		it("should handle empty cells array", () => {
			const result = shiftBordersForNewRow([], 0, "#000000");
			expect(result).toEqual([]);
		});

		it("should deep copy cells to avoid mutations", () => {
			const originalCells = [
				[createTestCell("A1", { right: { width: 2, color: "#ff0000" } })],
			];

			const result = shiftBordersForNewRow(originalCells, 0, "#000000");

			// Modify the result
			result[0][0].content = "Modified";
			if (result[0][0].borderSettings?.right) {
				result[0][0].borderSettings.right.width = 5;
			}

			// Original should be unchanged
			expect(originalCells[0][0].content).toBe("A1");
			expect(originalCells[0][0].borderSettings?.right?.width).toBe(2);
		});
	});

	describe("shiftBordersForNewColumn", () => {
		it("should preserve border structure when adding a new column", () => {
			const originalCells = [
				[
					createTestCell("A1", { right: { width: 3, color: "#ff0000" } }),
					createTestCell("B1", { left: { width: 1, color: "#0000ff" } }),
				],
				[
					createTestCell("A2", { right: { width: 2, color: "#00ff00" } }),
					createTestCell("B2"),
				],
			];

			const result = shiftBordersForNewColumn(originalCells, 1, "#000000");

			// The function should preserve existing border relationships
			// when inserting at index 1, it should handle the border relationship between col 0 and col 1
			expect(result).toHaveLength(2);
			expect(result[0]).toHaveLength(2);
			expect(result[1]).toHaveLength(2);

			// Check that borders are transferred to maintain visual continuity
			const cellA1 = result[0][0];
			const cellB1 = result[0][1];

			// A1 should have maintained its right border (3px red)
			expect(cellA1.borderSettings?.right).toEqual({
				width: 3,
				color: "#ff0000",
			});

			// Since B1 had a left border, A1's right border should be enhanced to match
			// or A1 should keep its stronger border (3px vs 1px)
			if (cellB1.borderSettings?.left) {
				// B1's left border might be preserved or modified based on conflict resolution
				expect(cellB1.borderSettings.left.width).toBeGreaterThanOrEqual(1);
			}
		});

		it("should deep copy cells to avoid mutations", () => {
			const originalCells = [
				[createTestCell("A1", { bottom: { width: 2, color: "#ff0000" } })],
			];

			const result = shiftBordersForNewColumn(originalCells, 0, "#000000");

			// Modify the result
			result[0][0].content = "Modified";
			if (result[0][0].borderSettings?.bottom) {
				result[0][0].borderSettings.bottom.width = 5;
			}

			// Original should be unchanged
			expect(originalCells[0][0].content).toBe("A1");
			expect(originalCells[0][0].borderSettings?.bottom?.width).toBe(2);
		});
	});

	describe("copyBorderSetting", () => {
		it("should copy border settings between cells", () => {
			const fromCell = createTestCell("From", {
				right: { width: 3, color: "#ff0000" },
			});
			const toCell = createTestCell("To");

			copyBorderSetting(fromCell, "right", toCell, "left", "#000000");

			expect(toCell.borderSettings?.left).toEqual({
				width: 3,
				color: "#ff0000",
			});
			expect(toCell.borderWidths.left).toBe(3);
		});

		it("should handle copying from borderWidths when no borderSettings exist", () => {
			const fromCell = createTestCell("From", undefined, {
				top: 2,
				right: 2,
				bottom: 2,
				left: 2,
			});
			const toCell = createTestCell("To");

			copyBorderSetting(fromCell, "right", toCell, "left", "#000000");

			expect(toCell.borderSettings?.left).toEqual({
				width: 2,
				color: "#000000",
			});
			expect(toCell.borderWidths.left).toBe(2);
		});

		it("should initialize borderSettings if it doesn't exist", () => {
			const fromCell = createTestCell("From", {
				top: { width: 1, color: "#ff0000" },
			});
			const toCell = createTestCell("To");

			expect(toCell.borderSettings).toBeUndefined();

			copyBorderSetting(fromCell, "top", toCell, "bottom", "#000000");

			expect(toCell.borderSettings).toBeDefined();
			expect(toCell.borderSettings?.bottom).toEqual({
				width: 1,
				color: "#ff0000",
			});
		});
	});
});

describe("Border Restoration Functions", () => {
	it("should restore borders when deleting a row in the middle", () => {
		// Create a 3-row table with borders that would be affected by deletion
		const cellsBeforeDeletion = [
			[
				createTestCell("A1", {
					bottom: { width: 2, color: "#ff0000" },
				}),
				createTestCell("B1", {
					bottom: { width: 1, color: "#00ff00" },
				}),
			],
			[
				// This row will be deleted
				createTestCell("A2_DELETE", {
					top: { width: 1, color: "#0000ff" },
					bottom: { width: 3, color: "#ffff00" },
				}),
				createTestCell("B2_DELETE", {
					top: { width: 2, color: "#ff00ff" },
					bottom: { width: 1, color: "#00ffff" },
				}),
			],
			[
				createTestCell("A3", {
					top: { width: 2, color: "#888888" },
				}),
				createTestCell("B3", {
					top: { width: 1, color: "#aaaaaa" },
				}),
			],
		];

		const result = restoreBordersForDeletedRow(
			cellsBeforeDeletion,
			1,
			"#000000",
		);

		// After deletion, row 0 should have the stronger borders from the conflict resolution
		// A1's bottom border (2px red) vs A2_DELETE's top border (1px blue) -> A1 wins
		expect(result[0][0].borderSettings?.bottom).toEqual({
			width: 2,
			color: "#ff0000",
		});

		// A2_DELETE's bottom border (3px yellow) vs A3's top border (2px gray) -> A2_DELETE wins
		// So A3 should get the 3px yellow border as its top border
		expect(result[2][0].borderSettings?.top).toEqual({
			width: 3,
			color: "#ffff00",
		});
	});

	it("should restore borders when deleting a column in the middle", () => {
		// Create a 3-column table with borders that would be affected by deletion
		const cellsBeforeDeletion = [
			[
				createTestCell("A1", {
					right: { width: 2, color: "#ff0000" },
				}),
				// This column will be deleted
				createTestCell("B1_DELETE", {
					left: { width: 1, color: "#0000ff" },
					right: { width: 3, color: "#ffff00" },
				}),
				createTestCell("C1", {
					left: { width: 2, color: "#888888" },
				}),
			],
		];

		const result = restoreBordersForDeletedColumn(
			cellsBeforeDeletion,
			1,
			"#000000",
		);

		// After deletion, A1 should have the stronger borders from the conflict resolution
		// A1's right border (2px red) vs B1_DELETE's left border (1px blue) -> A1 wins
		expect(result[0][0].borderSettings?.right).toEqual({
			width: 2,
			color: "#ff0000",
		});

		// B1_DELETE's right border (3px yellow) vs C1's left border (2px gray) -> B1_DELETE wins
		// So C1 should get the 3px yellow border as its left border
		expect(result[0][2].borderSettings?.left).toEqual({
			width: 3,
			color: "#ffff00",
		});
	});

	it("should handle edge cases for row deletion", () => {
		const cells = [[createTestCell("A1")], [createTestCell("A2")]];

		// Deleting first row (index 0) - no border merging should happen
		const result1 = restoreBordersForDeletedRow(cells, 0, "#000000");
		expect(result1).toEqual(cells);

		// Deleting last row (index 1) - no border merging should happen
		const result2 = restoreBordersForDeletedRow(cells, 1, "#000000");
		expect(result2).toEqual(cells);
	});

	it("should handle edge cases for column deletion", () => {
		const cells = [[createTestCell("A1"), createTestCell("B1")]];

		// Deleting first column (index 0) - no border merging should happen
		const result1 = restoreBordersForDeletedColumn(cells, 0, "#000000");
		expect(result1).toEqual(cells);

		// Deleting last column (index 1) - no border merging should happen
		const result2 = restoreBordersForDeletedColumn(cells, 1, "#000000");
		expect(result2).toEqual(cells);
	});
});

describe("Integration: Complete Border Shifting Workflow", () => {
	it("should maintain border relationships when adding a row in the middle", () => {
		// Create a 2x2 table with specific borders
		const originalCells = [
			[
				createTestCell("A1", {
					bottom: { width: 3, color: "#ff0000" },
					right: { width: 2, color: "#00ff00" },
				}),
				createTestCell("B1", {
					bottom: { width: 2, color: "#0000ff" },
					left: { width: 1, color: "#ffff00" },
				}),
			],
			[
				createTestCell("A2", {
					top: { width: 1, color: "#ff00ff" },
					right: { width: 3, color: "#00ffff" },
				}),
				createTestCell("B2", {
					top: { width: 1, color: "#000000" },
					left: { width: 2, color: "#ffffff" },
				}),
			],
		];

		// Simulate adding a row at index 1 (between row 0 and row 1)
		const shiftedCells = shiftBordersForNewRow(originalCells, 1, "#888888");

		// Verify that stronger borders are preserved
		// A1 had a 3px red bottom border, A2 had a 1px magenta top border
		// The stronger border (3px red) should win
		expect(shiftedCells[0][0].borderSettings?.bottom).toEqual({
			width: 3,
			color: "#ff0000",
		});

		// B1 had a 2px blue bottom border, B2 had a 1px black top border
		// The stronger border (2px blue) should win
		expect(shiftedCells[0][1].borderSettings?.bottom).toEqual({
			width: 2,
			color: "#0000ff",
		});

		// Original horizontal borders should be unaffected by row insertion
		expect(shiftedCells[0][0].borderSettings?.right).toEqual({
			width: 2,
			color: "#00ff00",
		});
		expect(shiftedCells[1][0].borderSettings?.right).toEqual({
			width: 3,
			color: "#00ffff",
		});
	});

	it("should maintain border relationships when adding a column in the middle", () => {
		// Create a 2x2 table with specific borders
		const originalCells = [
			[
				createTestCell("A1", {
					right: { width: 3, color: "#ff0000" },
					bottom: { width: 2, color: "#00ff00" },
				}),
				createTestCell("B1", {
					left: { width: 1, color: "#0000ff" },
					bottom: { width: 2, color: "#ffff00" },
				}),
			],
			[
				createTestCell("A2", {
					right: { width: 2, color: "#ff00ff" },
					top: { width: 1, color: "#00ffff" },
				}),
				createTestCell("B2", {
					left: { width: 1, color: "#000000" },
					top: { width: 2, color: "#ffffff" },
				}),
			],
		];

		// Simulate adding a column at index 1 (between col 0 and col 1)
		const shiftedCells = shiftBordersForNewColumn(originalCells, 1, "#888888");

		// Verify that stronger borders are preserved
		// A1 had a 3px red right border, B1 had a 1px blue left border
		// The stronger border (3px red) should win
		expect(shiftedCells[0][0].borderSettings?.right).toEqual({
			width: 3,
			color: "#ff0000",
		});

		// A2 had a 2px magenta right border, B2 had a 1px black left border
		// The stronger border (2px magenta) should win
		expect(shiftedCells[1][0].borderSettings?.right).toEqual({
			width: 2,
			color: "#ff00ff",
		});

		// Original vertical borders should be unaffected by column insertion
		expect(shiftedCells[0][0].borderSettings?.bottom).toEqual({
			width: 2,
			color: "#00ff00",
		});
		expect(shiftedCells[0][1].borderSettings?.bottom).toEqual({
			width: 2,
			color: "#ffff00",
		});
	});
});

describe("End-to-End Border Preservation Test", () => {
	it("should maintain original borders after adding and removing rows/columns", () => {
		// Create a 5x4 table (5 columns, 4 rows) with specific borders
		const originalCells = [
			[
				createTestCell("A1", {
					right: { width: 3, color: "#ff0000" },
					bottom: { width: 2, color: "#00ff00" },
				}),
				createTestCell("B1", {
					right: { width: 1, color: "#0000ff" },
					bottom: { width: 1, color: "#ffff00" },
				}),
				createTestCell("C1", {
					right: { width: 2, color: "#ff00ff" },
					bottom: { width: 3, color: "#00ffff" },
				}),
				createTestCell("D1", {
					right: { width: 1, color: "#888888" },
					bottom: { width: 1, color: "#aaaaaa" },
				}),
				createTestCell("E1", {
					bottom: { width: 2, color: "#111111" },
				}),
			],
			[
				createTestCell("A2", {
					right: { width: 2, color: "#222222" },
					bottom: { width: 1, color: "#333333" },
				}),
				createTestCell("B2", {
					right: { width: 3, color: "#444444" },
					bottom: { width: 2, color: "#555555" },
				}),
				createTestCell("C2", {
					right: { width: 1, color: "#666666" },
					bottom: { width: 1, color: "#777777" },
				}),
				createTestCell("D2", {
					right: { width: 2, color: "#999999" },
					bottom: { width: 3, color: "#bbbbbb" },
				}),
				createTestCell("E2", {
					bottom: { width: 1, color: "#cccccc" },
				}),
			],
			[
				createTestCell("A3", {
					right: { width: 1, color: "#dddddd" },
					bottom: { width: 2, color: "#eeeeee" },
				}),
				createTestCell("B3", {
					right: { width: 2, color: "#abcdef" },
					bottom: { width: 1, color: "#fedcba" },
				}),
				createTestCell("C3", {
					right: { width: 3, color: "#123456" },
					bottom: { width: 2, color: "#654321" },
				}),
				createTestCell("D3", {
					right: { width: 1, color: "#789abc" },
					bottom: { width: 1, color: "#cba987" },
				}),
				createTestCell("E3", {
					bottom: { width: 3, color: "#147258" },
				}),
			],
			[
				createTestCell("A4", {
					right: { width: 2, color: "#852741" },
				}),
				createTestCell("B4", {
					right: { width: 1, color: "#963852" },
				}),
				createTestCell("C4", {
					right: { width: 2, color: "#159753" },
				}),
				createTestCell("D4", {
					right: { width: 3, color: "#486159" },
				}),
				createTestCell("E4"),
			],
		];

		// Step 1: Save the original state for comparison
		const originalBorderSnapshot = JSON.parse(JSON.stringify(originalCells));

		// Step 2: Add a new row at index 2 (between row 1 and 2)
		const afterRowAdd = shiftBordersForNewRow(
			JSON.parse(JSON.stringify(originalCells)),
			2,
			"#000000",
		);

		// Simulate adding the actual new row (this would happen in addRow function)
		const newRowCells = [
			createTestCell("A_NEW", undefined, {
				top: 1,
				right: 1,
				bottom: 1,
				left: 1,
			}),
			createTestCell("B_NEW", undefined, {
				top: 1,
				right: 1,
				bottom: 1,
				left: 1,
			}),
			createTestCell("C_NEW", undefined, {
				top: 1,
				right: 1,
				bottom: 1,
				left: 1,
			}),
			createTestCell("D_NEW", undefined, {
				top: 1,
				right: 1,
				bottom: 1,
				left: 1,
			}),
			createTestCell("E_NEW", undefined, {
				top: 1,
				right: 1,
				bottom: 1,
				left: 1,
			}),
		];
		afterRowAdd.splice(2, 0, newRowCells);

		// Step 3: Add a new column at index 2 (between column 1 and 2)
		const afterColumnAdd = shiftBordersForNewColumn(
			JSON.parse(JSON.stringify(afterRowAdd)),
			2,
			"#000000",
		);

		// Simulate adding the actual new column cells
		for (let i = 0; i < afterColumnAdd.length; i++) {
			const newCell = createTestCell(`COL_NEW_${i}`, undefined, {
				top: 1,
				right: 1,
				bottom: 1,
				left: 1,
			});
			afterColumnAdd[i].splice(2, 0, newCell);
		}

		// At this point we have a 6x5 table with added row and column

		// Step 4: Delete the added column (index 2) with border restoration
		const afterColumnDeleteWithBorderRestore = restoreBordersForDeletedColumn(
			afterColumnAdd,
			2,
			"#000000",
		);
		const afterColumnDelete = afterColumnDeleteWithBorderRestore.map((row) => {
			const newRow = [...row];
			newRow.splice(2, 1); // Remove column at index 2
			return newRow;
		});

		// Step 5: Delete the added row (index 2) with border restoration
		const afterRowDeleteWithBorderRestore = restoreBordersForDeletedRow(
			afterColumnDelete,
			2,
			"#000000",
		);
		const afterRowDelete = [...afterRowDeleteWithBorderRestore];
		afterRowDelete.splice(2, 1); // Remove row at index 2

		// Step 6: Compare with original - should be very close or identical
		expect(afterRowDelete).toHaveLength(originalBorderSnapshot.length);
		expect(afterRowDelete[0]).toHaveLength(originalBorderSnapshot[0].length);

		// Verify key borders are preserved correctly
		// A1's right border should be preserved
		expect(afterRowDelete[0][0].borderSettings?.right).toEqual(
			originalBorderSnapshot[0][0].borderSettings?.right,
		);

		// A1's bottom border should be preserved
		expect(afterRowDelete[0][0].borderSettings?.bottom).toEqual(
			originalBorderSnapshot[0][0].borderSettings?.bottom,
		);

		// Check a few more critical borders
		expect(afterRowDelete[1][1].borderSettings?.right).toEqual(
			originalBorderSnapshot[1][1].borderSettings?.right,
		);
		expect(afterRowDelete[2][2].borderSettings?.bottom).toEqual(
			originalBorderSnapshot[2][2].borderSettings?.bottom,
		);

		console.log(
			"✅ Border preservation test passed - user scenario now works correctly!",
		);
	});

	it("should demonstrate the exact user scenario: 5x4 grid with borders", () => {
		// Create the exact 5x4 grid the user mentioned
		const original5x4Grid = Array(4)
			.fill(null)
			.map((_, rowIndex) =>
				Array(5)
					.fill(null)
					.map((_, colIndex) =>
						createTestCell(`R${rowIndex + 1}C${colIndex + 1}`, {
							right:
								colIndex < 4
									? {
											width: ((rowIndex + colIndex) % 3) + 1,
											color: `#${(rowIndex * 5 + colIndex).toString(16).padStart(6, "0")}`,
										}
									: undefined,
							bottom:
								rowIndex < 3
									? {
											width: ((rowIndex + colIndex + 1) % 3) + 1,
											color: `#${((rowIndex + 1) * 5 + colIndex).toString(16).padStart(6, "0")}`,
										}
									: undefined,
						}),
					),
			);

		// Save original state
		const originalSnapshot = JSON.parse(JSON.stringify(original5x4Grid));

		// User's workflow: Add row in middle (index 2)
		let workingTable = shiftBordersForNewRow(
			JSON.parse(JSON.stringify(original5x4Grid)),
			2,
			"#000000",
		);
		const newRow = Array(5)
			.fill(null)
			.map((_, i) =>
				createTestCell(`NEW_R${i}`, undefined, {
					top: 1,
					right: 1,
					bottom: 1,
					left: 1,
				}),
			);
		workingTable.splice(2, 0, newRow);

		// Add column in middle (index 2)
		workingTable = shiftBordersForNewColumn(workingTable, 2, "#000000");
		for (let i = 0; i < workingTable.length; i++) {
			const newCell = createTestCell(`NEW_C${i}`, undefined, {
				top: 1,
				right: 1,
				bottom: 1,
				left: 1,
			});
			workingTable[i].splice(2, 0, newCell);
		}

		// Now we have a 6x5 table. Delete the new column (index 2)
		workingTable = restoreBordersForDeletedColumn(workingTable, 2, "#000000");
		workingTable = workingTable.map((row) => {
			const newRow = [...row];
			newRow.splice(2, 1);
			return newRow;
		});

		// Delete the new row (index 2)
		workingTable = restoreBordersForDeletedRow(workingTable, 2, "#000000");
		workingTable.splice(2, 1);

		// Should be back to original 5x4
		expect(workingTable).toHaveLength(4);
		expect(workingTable[0]).toHaveLength(5);

		// Verify borders are preserved
		expect(workingTable[0][0].borderSettings?.right?.width).toBe(
			originalSnapshot[0][0].borderSettings?.right?.width,
		);
		expect(workingTable[0][0].borderSettings?.right?.color).toBe(
			originalSnapshot[0][0].borderSettings?.right?.color,
		);

		console.log("✅ User's exact 5x4 grid scenario works perfectly!");
	});
});
